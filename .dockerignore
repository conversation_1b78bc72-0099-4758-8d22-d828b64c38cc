# Git
.git
.gitignore

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log
logs/

# Documentation (исключаем только в корне, папка docs/ нужна)
/*.md
!README.md

# Docker (исключаем только основные файлы)
Dockerfile
docker-compose.yml
.dockerignore

# Deployment (исключаем только шаблон)
.env.production

# Backup files
*.sql
!init.sql  # Исключение: init.sql нужен для инициализации БД
backup_*
